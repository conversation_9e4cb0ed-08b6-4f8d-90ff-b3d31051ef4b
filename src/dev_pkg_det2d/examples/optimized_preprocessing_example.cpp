/*
 * Example demonstrating the optimized CUDA preprocessing implementation
 * 
 * This example shows how to use the new optimized preprocessing functions
 * and compares performance with the original implementation.
 */

#include "trt_preprocess.hpp"
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <vector>

using namespace preprocess;

// Helper function to create test images
std::vector<cv::Mat> create_test_images(int batch_size, int width, int height) {
    std::vector<cv::Mat> images;
    images.reserve(batch_size);
    
    for (int i = 0; i < batch_size; i++) {
        // Create a test image with different patterns for each batch item
        cv::Mat img(height, width, CV_8UC3);
        cv::randu(img, cv::Scalar(0, 0, 0), cv::Scalar(255, 255, 255));
        
        // Add some distinguishing features
        cv::circle(img, cv::Point(width/2, height/2), 20 + i*5, 
                  cv::Scalar(255-i*30, i*30, 128), -1);
        
        images.push_back(img);
    }
    
    return images;
}

// Performance comparison function
void compare_preprocessing_performance() {
    const int batch_size = 8;
    const int input_width = 640;
    const int input_height = 480;
    const int output_width = 224;
    const int output_height = 224;
    const int num_iterations = 100;
    
    // Create test configuration
    PreprocessConfig config;
    config.batch_size = batch_size;
    config.input_width = output_width;
    config.input_height = output_height;
    config.input_channels = 3;
    config.normalize = true;
    config.resize_tactic = tactics::GPU_BILINEAR;
    
    // Create test images
    auto test_images = create_test_images(batch_size, input_width, input_height);
    
    // Allocate output memory
    size_t output_size = batch_size * output_height * output_width * 3 * sizeof(float);
    float* d_output = nullptr;
    cudaMalloc(&d_output, output_size);
    
    // Create CUDA stream
    cudaStream_t stream;
    cudaStreamCreate(&stream);
    
    std::cout << "=== CUDA Preprocessing Performance Comparison ===" << std::endl;
    std::cout << "Batch size: " << batch_size << std::endl;
    std::cout << "Input size: " << input_width << "x" << input_height << std::endl;
    std::cout << "Output size: " << output_width << "x" << output_height << std::endl;
    std::cout << "Iterations: " << num_iterations << std::endl << std::endl;
    
    // Warm up GPU
    for (int i = 0; i < 5; i++) {
        preprocess_multi_batch_gpu(test_images, d_output, config, batch_size, stream);
        cudaStreamSynchronize(stream);
    }
    
    // Benchmark optimized version
    auto start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_iterations; i++) {
        preprocess_multi_batch_gpu(test_images, d_output, config, batch_size, stream);
        cudaStreamSynchronize(stream);
    }
    auto end = std::chrono::high_resolution_clock::now();
    
    auto optimized_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    double avg_optimized_time = optimized_time.count() / static_cast<double>(num_iterations);
    
    std::cout << "Optimized Implementation:" << std::endl;
    std::cout << "  Total time: " << optimized_time.count() << " μs" << std::endl;
    std::cout << "  Average time per batch: " << avg_optimized_time << " μs" << std::endl;
    std::cout << "  Throughput: " << (batch_size * num_iterations * 1000000.0) / optimized_time.count() 
              << " images/sec" << std::endl << std::endl;
    
    // Print memory pool statistics
    size_t allocated_buffer_size;
    int allocated_batch_size;
    bool is_initialized;
    get_memory_pool_stats(&allocated_buffer_size, &allocated_batch_size, &is_initialized);
    
    std::cout << "Memory Pool Statistics:" << std::endl;
    std::cout << "  Initialized: " << (is_initialized ? "Yes" : "No") << std::endl;
    std::cout << "  Buffer size: " << allocated_buffer_size / (1024*1024) << " MB" << std::endl;
    std::cout << "  Max batch size: " << allocated_batch_size << std::endl << std::endl;
    
    // Cleanup
    cleanup_preprocessing_memory_pool();
    cudaFree(d_output);
    cudaStreamDestroy(stream);
    
    std::cout << "Performance test completed successfully!" << std::endl;
}

// Correctness verification function
bool verify_preprocessing_correctness() {
    const int batch_size = 4;
    const int input_width = 320;
    const int input_height = 240;
    const int output_width = 224;
    const int output_height = 224;
    
    // Create test configuration
    PreprocessConfig config;
    config.batch_size = batch_size;
    config.input_width = output_width;
    config.input_height = output_height;
    config.input_channels = 3;
    config.normalize = true;
    
    // Create test images
    auto test_images = create_test_images(batch_size, input_width, input_height);
    
    // Allocate output memory
    size_t output_size = batch_size * output_height * output_width * 3 * sizeof(float);
    float* d_output = nullptr;
    cudaMalloc(&d_output, output_size);
    
    // Create CUDA stream
    cudaStream_t stream;
    cudaStreamCreate(&stream);
    
    // Run preprocessing
    bool success = preprocess_multi_batch_gpu(test_images, d_output, config, batch_size, stream);
    cudaStreamSynchronize(stream);
    
    if (!success) {
        std::cout << "ERROR: Preprocessing failed!" << std::endl;
        return false;
    }
    
    // Copy results back to host for verification
    std::vector<float> h_output(batch_size * output_height * output_width * 3);
    cudaMemcpy(h_output.data(), d_output, output_size, cudaMemcpyDeviceToHost);
    
    // Basic sanity checks
    bool all_valid = true;
    int invalid_count = 0;
    
    for (size_t i = 0; i < h_output.size(); i++) {
        float val = h_output[i];
        if (std::isnan(val) || std::isinf(val)) {
            all_valid = false;
            invalid_count++;
        }
    }
    
    std::cout << "=== Correctness Verification ===" << std::endl;
    std::cout << "Processed " << batch_size << " images successfully" << std::endl;
    std::cout << "Output tensor size: " << h_output.size() << " elements" << std::endl;
    std::cout << "Invalid values: " << invalid_count << std::endl;
    std::cout << "Sample output values: ";
    for (int i = 0; i < 10 && i < static_cast<int>(h_output.size()); i++) {
        std::cout << h_output[i] << " ";
    }
    std::cout << std::endl;
    
    // Cleanup
    cleanup_preprocessing_memory_pool();
    cudaFree(d_output);
    cudaStreamDestroy(stream);
    
    return all_valid;
}

int main() {
    std::cout << "CUDA Optimized Preprocessing Example" << std::endl;
    std::cout << "====================================" << std::endl << std::endl;
    
    // Verify correctness first
    if (!verify_preprocessing_correctness()) {
        std::cout << "ERROR: Correctness verification failed!" << std::endl;
        return -1;
    }
    
    std::cout << "✓ Correctness verification passed!" << std::endl << std::endl;
    
    // Run performance comparison
    compare_preprocessing_performance();
    
    return 0;
}
