# CMakeLists.txt for CUDA preprocessing optimization examples

cmake_minimum_required(VERSION 3.8)

# Find required packages
find_package(OpenCV REQUIRED)
find_package(CUDA REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include/dev_pkg_det2d)
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${CUDA_INCLUDE_DIRS})

# Set CUDA flags
set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -gencode arch=compute_75,code=sm_75")
set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -gencode arch=compute_80,code=sm_80")
set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -gencode arch=compute_86,code=sm_86")

# Create the preprocessing optimization example
add_executable(optimized_preprocessing_example
    optimized_preprocessing_example.cpp
    ../src/cu/trt_preprocess.cu
    ../src/trt/trt_logger.cpp
)

# Set properties for CUDA compilation
set_property(TARGET optimized_preprocessing_example PROPERTY CUDA_SEPARABLE_COMPILATION ON)

# Link libraries
target_link_libraries(optimized_preprocessing_example
    ${OpenCV_LIBS}
    ${CUDA_LIBRARIES}
    cudart
    cublas
    curand
)

# Set C++ standard
set_target_properties(optimized_preprocessing_example PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# Add compile definitions
target_compile_definitions(optimized_preprocessing_example PRIVATE
    ENABLE_TEXT_BACKEND_STB
)

# Installation
install(TARGETS optimized_preprocessing_example
    RUNTIME DESTINATION bin
)

# Add a custom target for running the example
add_custom_target(run_preprocessing_example
    COMMAND optimized_preprocessing_example
    DEPENDS optimized_preprocessing_example
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running CUDA preprocessing optimization example"
)
