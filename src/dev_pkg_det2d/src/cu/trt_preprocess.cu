#include "cuda_runtime_api.h"
#include "stdio.h"
#include <iostream>
#include "trt_preprocess.hpp"

namespace preprocess{

TransInfo    trans;
AffineMatrix affine_matrix;

std::vector<TransInfo>    vec_trans;
std::vector<AffineMatrix> vec_affine_matrix;

void params_init(int batch_size){
    vec_trans.resize(batch_size);
    vec_affine_matrix.resize(batch_size);
}

void warpaffine_init(int srcH, int srcW, int tarH, int tarW){
    trans.src_h = srcH;
    trans.src_w = srcW;
    trans.tar_h = tarH;
    trans.tar_w = tarW;
    affine_matrix.init(trans);
}

void warpaffine_init(int srcH, int srcW, int tarH, int tarW, int bIdx){
    vec_trans[bIdx].src_h = srcH;
    vec_trans[bIdx].src_w = srcW;
    vec_trans[bIdx].tar_h = tarH;
    vec_trans[bIdx].tar_w = tarW;
    vec_affine_matrix[bIdx].init(vec_trans[bIdx]);
}

__host__ __device__ void affine_transformation(
    float trans_matrix[6], 
    int src_x, int src_y, 
    float* tar_x, float* tar_y)
{
    *tar_x = trans_matrix[0] * src_x + trans_matrix[1] * src_y + trans_matrix[2];
    *tar_y = trans_matrix[3] * src_x + trans_matrix[4] * src_y + trans_matrix[5];
}

__global__ void nearest_BGR2RGB_nhwc2nchw_norm_kernel(
    float* tar, uint8_t* src, 
    int tarW, int tarH, 
    int srcW, int srcH,
    float scaled_w, float scaled_h,
    float* d_mean, float* d_std) 
{
    // nearest neighbour -- resized之后的图tar上的坐标
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    // nearest neighbour -- 计算最近坐标
    int src_y = floor((float)y * scaled_h);
    int src_x = floor((float)x * scaled_w);

    if (src_x < 0 || src_y < 0 || src_x > srcW || src_y > srcH) {
        // nearest neighbour -- 对于越界的部分，不进行计算
    } else {
        // nearest neighbour -- 计算tar中对应坐标的索引
        int tarIdx  = y * tarW + x;
        int tarArea = tarW * tarH;

        // nearest neighbour -- 计算src中最近邻坐标的索引
        int srcIdx = (src_y * srcW + src_x) * 3;

        // nearest neighbour -- 实现nearest beighbour的resize + BGR2RGB + nhwc2nchw + norm
        tar[tarIdx + tarArea * 0] = (src[srcIdx + 2] / 255.0f - d_mean[2]) / d_std[2];
        tar[tarIdx + tarArea * 1] = (src[srcIdx + 1] / 255.0f - d_mean[1]) / d_std[1];
        tar[tarIdx + tarArea * 2] = (src[srcIdx + 0] / 255.0f - d_mean[0]) / d_std[0];
    }
}

__global__ void bilinear_BGR2RGB_nhwc2nchw_norm_kernel(
    float* tar, uint8_t* src, 
    int tarW, int tarH, 
    int srcW, int srcH, 
    float scaled_w, float scaled_h,
    float* d_mean, float* d_std) 
{

    // bilinear interpolation -- resized之后的图tar上的坐标
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    // // bilinear interpolation -- 计算x,y映射到原图时最近的4个坐标
    int src_y1 = floor((y + 0.5) * scaled_h - 0.5);
    int src_x1 = floor((x + 0.5) * scaled_w - 0.5);
    int src_y2 = src_y1 + 1;
    int src_x2 = src_x1 + 1;

    if (src_y1 < 0 || src_x1 < 0 || src_y2 > srcH || src_x2 > srcW) {
        // bilinear interpolation -- 对于越界的坐标不进行计算
    } else {
        // bilinear interpolation -- 计算原图上的坐标(浮点类型)在0~1之间的值
        float th   = ((y + 0.5) * scaled_h - 0.5) - src_y1;
        float tw   = ((x + 0.5) * scaled_w - 0.5) - src_x1;

        // bilinear interpolation -- 计算面积(这里建议自己手画一张图来理解一下)
        float a1_1 = (1.0 - tw) * (1.0 - th);  //右下
        float a1_2 = tw * (1.0 - th);          //左下
        float a2_1 = (1.0 - tw) * th;          //右上
        float a2_2 = tw * th;                  //左上

        // bilinear interpolation -- 计算4个坐标所对应的索引
        int srcIdx1_1 = (src_y1 * srcW + src_x1) * 3;  //左上
        int srcIdx1_2 = (src_y1 * srcW + src_x2) * 3;  //右上
        int srcIdx2_1 = (src_y2 * srcW + src_x1) * 3;  //左下
        int srcIdx2_2 = (src_y2 * srcW + src_x2) * 3;  //右下

        // bilinear interpolation -- 计算resized之后的图的索引
        int tarIdx    = y * tarW  + x;
        int tarArea   = tarW * tarH;

        // bilinear interpolation -- 实现bilinear interpolation的resize + BGR2RGB + NHWC2NCHW normalization
        // 注意，这里tar和src进行遍历的方式是不一样的
        tar[tarIdx + tarArea * 0] = 
            (round((a1_1 * src[srcIdx1_1 + 2] + 
                   a1_2 * src[srcIdx1_2 + 2] +
                   a2_1 * src[srcIdx2_1 + 2] +
                   a2_2 * src[srcIdx2_2 + 2])) / 255.0f - d_mean[2]) / d_std[2];

        tar[tarIdx + tarArea * 1] = 
            (round((a1_1 * src[srcIdx1_1 + 1] + 
                   a1_2 * src[srcIdx1_2 + 1] +
                   a2_1 * src[srcIdx2_1 + 1] +
                   a2_2 * src[srcIdx2_2 + 1])) / 255.0f - d_mean[1]) / d_std[1];

        tar[tarIdx + tarArea * 2] = 
            (round((a1_1 * src[srcIdx1_1 + 0] + 
                   a1_2 * src[srcIdx1_2 + 0] +
                   a2_1 * src[srcIdx2_1 + 0] +
                   a2_2 * src[srcIdx2_2 + 0])) / 255.0f - d_mean[0]) / d_std[0];

    }
}

__global__ void bilinear_BGR2RGB_nhwc2nchw_shift_norm_kernel(
    float* tar, uint8_t* src, 
    int tarW, int tarH, 
    int srcW, int srcH, 
    float scaled_w, float scaled_h,
    float* d_mean, float* d_std) 
{
    // resized之后的图tar上的坐标
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    // bilinear interpolation -- 计算x,y映射到原图时最近的4个坐标
    int src_y1 = floor((y + 0.5) * scaled_h - 0.5);
    int src_x1 = floor((x + 0.5) * scaled_w - 0.5);
    int src_y2 = src_y1 + 1;
    int src_x2 = src_x1 + 1;

    if (src_y1 < 0 || src_x1 < 0 || src_y2 > srcH || src_x2 > srcW) {
        // bilinear interpolation -- 对于越界的坐标不进行计算
    } else {
        // bilinear interpolation -- 计算原图上的坐标(浮点类型)在0~1之间的值
        float th   = (float)y * scaled_h - src_y1;
        float tw   = (float)x * scaled_w - src_x1;

        // bilinear interpolation -- 计算面积(这里建议自己手画一张图来理解一下)
        float a1_1 = (1.0 - tw) * (1.0 - th);  // 右下
        float a1_2 = tw * (1.0 - th);          // 左下
        float a2_1 = (1.0 - tw) * th;          // 右上
        float a2_2 = tw * th;                  // 左上

        // bilinear interpolation -- 计算4个坐标所对应的索引
        int srcIdx1_1 = (src_y1 * srcW + src_x1) * 3;  // 左上
        int srcIdx1_2 = (src_y1 * srcW + src_x2) * 3;  // 右上
        int srcIdx2_1 = (src_y2 * srcW + src_x1) * 3;  // 左下
        int srcIdx2_2 = (src_y2 * srcW + src_x2) * 3;  // 右下

        // bilinear interpolation -- 计算原图在目标图中的x, y方向上的偏移量
        y = y - int(srcH / (scaled_h * 2)) + int(tarH / 2);
        x = x - int(srcW / (scaled_w * 2)) + int(tarW / 2);

        // bilinear interpolation -- 计算resized之后的图的索引
        int tarIdx    = (y * tarW  + x) * 3;
        int tarArea   = tarW * tarH;

        // bilinear interpolation -- 实现bilinear interpolation + BGR2RGB + shift + nhwc2nchw
        tar[tarIdx + tarArea * 0] = 
            (round((a1_1 * src[srcIdx1_1 + 2] + 
                   a1_2 * src[srcIdx1_2 + 2] +
                   a2_1 * src[srcIdx2_1 + 2] +
                   a2_2 * src[srcIdx2_2 + 2])) / 255.0f - d_mean[2]) / d_std[2];

        tar[tarIdx + tarArea * 1] = 
            (round((a1_1 * src[srcIdx1_1 + 1] + 
                   a1_2 * src[srcIdx1_2 + 1] +
                   a2_1 * src[srcIdx2_1 + 1] +
                   a2_2 * src[srcIdx2_2 + 1])) / 255.0f - d_mean[1]) / d_std[1];

        tar[tarIdx + tarArea * 2] = 
            (round((a1_1 * src[srcIdx1_1 + 0] + 
                   a1_2 * src[srcIdx1_2 + 0] +
                   a2_1 * src[srcIdx2_1 + 0] +
                   a2_2 * src[srcIdx2_2 + 0])) / 255.0f - d_mean[0]) / d_std[0];
    }
}


__global__ void nearest_BGR2RGB_nhwc2nchw_kernel(
    float* tar, uint8_t* src, 
    int tarW, int tarH, 
    int srcW, int srcH,
    float scaled_w, float scaled_h)
{
    // nearest neighbour -- resized之后的图tar上的坐标
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    // nearest neighbour -- 计算最近坐标
    int src_y = floor((float)y * scaled_h);
    int src_x = floor((float)x * scaled_w);

    if (src_x < 0 || src_y < 0 || src_x > srcW || src_y > srcH) {
        // nearest neighbour -- 对于越界的部分，不进行计算
    } else {
        // nearest neighbour -- 计算tar中对应坐标的索引
        int tarIdx  = y * tarW + x;
        int tarArea = tarW * tarH;

        // nearest neighbour -- 计算src中最近邻坐标的索引
        int srcIdx = (src_y * srcW + src_x) * 3;

        // nearest neighbour -- 实现nearest beighbour的resize + BGR2RGB + nhwc2nchw + norm
        tar[tarIdx + tarArea * 0] = src[srcIdx + 2] / 255.0f;
        tar[tarIdx + tarArea * 1] = src[srcIdx + 1] / 255.0f;
        tar[tarIdx + tarArea * 2] = src[srcIdx + 0] / 255.0f;
    }
}

__global__ void bilinear_BGR2RGB_nhwc2nchw_kernel(
    float* tar, uint8_t* src, 
    int tarW, int tarH, 
    int srcW, int srcH, 
    float scaled_w, float scaled_h)
{

    // bilinear interpolation -- resized之后的图tar上的坐标
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    // // bilinear interpolation -- 计算x,y映射到原图时最近的4个坐标
    int src_y1 = floor((y + 0.5) * scaled_h - 0.5);
    int src_x1 = floor((x + 0.5) * scaled_w - 0.5);
    int src_y2 = src_y1 + 1;
    int src_x2 = src_x1 + 1;

    if (src_y1 < 0 || src_x1 < 0 || src_y2 > srcH || src_x2 > srcW) {
        // bilinear interpolation -- 对于越界的坐标不进行计算
    } else {
        // bilinear interpolation -- 计算原图上的坐标(浮点类型)在0~1之间的值
        float th   = ((y + 0.5) * scaled_h - 0.5) - src_y1;
        float tw   = ((x + 0.5) * scaled_w - 0.5) - src_x1;

        // bilinear interpolation -- 计算面积(这里建议自己手画一张图来理解一下)
        float a1_1 = (1.0 - tw) * (1.0 - th);  //右下
        float a1_2 = tw * (1.0 - th);          //左下
        float a2_1 = (1.0 - tw) * th;          //右上
        float a2_2 = tw * th;                  //左上

        // bilinear interpolation -- 计算4个坐标所对应的索引
        int srcIdx1_1 = (src_y1 * srcW + src_x1) * 3;  //左上
        int srcIdx1_2 = (src_y1 * srcW + src_x2) * 3;  //右上
        int srcIdx2_1 = (src_y2 * srcW + src_x1) * 3;  //左下
        int srcIdx2_2 = (src_y2 * srcW + src_x2) * 3;  //右下

        // bilinear interpolation -- 计算resized之后的图的索引
        int tarIdx    = y * tarW  + x;
        int tarArea   = tarW * tarH;

        // bilinear interpolation -- 实现bilinear interpolation的resize + BGR2RGB + NHWC2NCHW normalization
        // 注意，这里tar和src进行遍历的方式是不一样的
        tar[tarIdx + tarArea * 0] = 
            round((a1_1 * src[srcIdx1_1 + 2] + 
                   a1_2 * src[srcIdx1_2 + 2] +
                   a2_1 * src[srcIdx2_1 + 2] +
                   a2_2 * src[srcIdx2_2 + 2])) / 255.0f;

        tar[tarIdx + tarArea * 1] = 
            round((a1_1 * src[srcIdx1_1 + 1] + 
                   a1_2 * src[srcIdx1_2 + 1] +
                   a2_1 * src[srcIdx2_1 + 1] +
                   a2_2 * src[srcIdx2_2 + 1])) / 255.0f;

        tar[tarIdx + tarArea * 2] = 
            round((a1_1 * src[srcIdx1_1 + 0] + 
                   a1_2 * src[srcIdx1_2 + 0] +
                   a2_1 * src[srcIdx2_1 + 0] +
                   a2_2 * src[srcIdx2_2 + 0])) / 255.0f;

    }
}

__global__ void bilinear_BGR2RGB_nhwc2nchw_shift_kernel(
    float* tar, uint8_t* src, 
    int tarW, int tarH, 
    int srcW, int srcH, 
    float scaled_w, float scaled_h)
{
    // resized之后的图tar上的坐标
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;


    // bilinear interpolation -- 计算x,y映射到原图时最近的4个坐标
    int src_y1 = floor((y + 0.5) * scaled_h - 0.5);
    int src_x1 = floor((x + 0.5) * scaled_w - 0.5);
    int src_y2 = src_y1 + 1;
    int src_x2 = src_x1 + 1;

    if (src_y1 < 0 || src_x1 < 0 || src_y2 > srcH || src_x2 > srcW) {
        // bilinear interpolation -- 对于越界的坐标不进行计算
    } else {
        // bilinear interpolation -- 计算原图上的坐标(浮点类型)在0~1之间的值
        float th   = (float)y * scaled_h - src_y1;
        float tw   = (float)x * scaled_w - src_x1;

        // bilinear interpolation -- 计算面积(这里建议自己手画一张图来理解一下)
        float a1_1 = (1.0 - tw) * (1.0 - th);  // 右下
        float a1_2 = tw * (1.0 - th);          // 左下
        float a2_1 = (1.0 - tw) * th;          // 右上
        float a2_2 = tw * th;                  // 左上

        // bilinear interpolation -- 计算4个坐标所对应的索引
        int srcIdx1_1 = (src_y1 * srcW + src_x1) * 3;  // 左上
        int srcIdx1_2 = (src_y1 * srcW + src_x2) * 3;  // 右上
        int srcIdx2_1 = (src_y2 * srcW + src_x1) * 3;  // 左下
        int srcIdx2_2 = (src_y2 * srcW + src_x2) * 3;  // 右下

        // bilinear interpolation -- 计算原图在目标图中的x, y方向上的偏移量
        y = y - int(srcH / (scaled_h * 2)) + int(tarH / 2);
        x = x - int(srcW / (scaled_w * 2)) + int(tarW / 2);

        // bilinear interpolation -- 计算resized之后的图的索引
        int tarIdx    = y * tarW  + x;
        int tarArea   = tarW * tarH;

        // bilinear interpolation -- 实现bilinear interpolation + BGR2RGB + shift + nhwc2nchw
        tar[tarIdx + tarArea * 0] = 
            round((a1_1 * src[srcIdx1_1 + 2] + 
                   a1_2 * src[srcIdx1_2 + 2] +
                   a2_1 * src[srcIdx2_1 + 2] +
                   a2_2 * src[srcIdx2_2 + 2])) / 255.0f;

        tar[tarIdx + tarArea * 1] = 
            round((a1_1 * src[srcIdx1_1 + 1] + 
                   a1_2 * src[srcIdx1_2 + 1] +
                   a2_1 * src[srcIdx2_1 + 1] +
                   a2_2 * src[srcIdx2_2 + 1])) / 255.0f;

        tar[tarIdx + tarArea * 2] = 
            round((a1_1 * src[srcIdx1_1 + 0] + 
                   a1_2 * src[srcIdx1_2 + 0] +
                   a2_1 * src[srcIdx2_1 + 0] +
                   a2_2 * src[srcIdx2_2 + 0])) / 255.0f;
    }
}

__global__ void warpaffine_BGR2RGB_kernel(
    float* tar, uint8_t* src, 
    TransInfo trans,
    AffineMatrix affine_matrix)
{
    float src_x, src_y;

    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    affine_transformation(affine_matrix.reverse, x + 0.5, y + 0.5, &src_x, &src_y);

    int src_x1 = floor(src_x - 0.5);
    int src_y1 = floor(src_y - 0.5);
    int src_x2 = src_x1 + 1;
    int src_y2 = src_y1 + 1;

    if (src_y1 < 0 || src_x1 < 0 || src_y1 > trans.src_h || src_x1 > trans.src_w) {
    } else {
        float tw   = src_x - src_x1;
        float th   = src_y - src_y1;

        float a1_1 = (1.0 - tw) * (1.0 - th);
        float a1_2 = tw * (1.0 - th);
        float a2_1 = (1.0 - tw) * th;
        float a2_2 = tw * th;

        int srcIdx1_1 = (src_y1 * trans.src_w + src_x1) * 3;
        int srcIdx1_2 = (src_y1 * trans.src_w + src_x2) * 3;
        int srcIdx2_1 = (src_y2 * trans.src_w + src_x1) * 3;
        int srcIdx2_2 = (src_y2 * trans.src_w + src_x2) * 3;

        int tarIdx    = y * trans.tar_w  + x;
        int tarArea   = trans.tar_w * trans.tar_h;

        tar[tarIdx + tarArea * 0] = 
            round((a1_1 * src[srcIdx1_1 + 2] + 
                   a1_2 * src[srcIdx1_2 + 2] +
                   a2_1 * src[srcIdx2_1 + 2] +
                   a2_2 * src[srcIdx2_2 + 2])) / 255.0f;

        tar[tarIdx + tarArea * 1] = 
            round((a1_1 * src[srcIdx1_1 + 1] + 
                   a1_2 * src[srcIdx1_2 + 1] +
                   a2_1 * src[srcIdx2_1 + 1] +
                   a2_2 * src[srcIdx2_2 + 1])) / 255.0f;

        tar[tarIdx + tarArea * 2] = 
            round((a1_1 * src[srcIdx1_1 + 0] + 
                   a1_2 * src[srcIdx1_2 + 0] +
                   a2_1 * src[srcIdx2_1 + 0] +
                   a2_2 * src[srcIdx2_2 + 0])) / 255.0f;
    }
}

void resize_bilinear_gpu(
    float* d_tar, uint8_t* d_src,
    int tarW, int tarH,
    int srcW, int srcH,
    float* d_mean, float* d_std,
    tactics tac)
{
    // Input validation
    if (!d_tar || !d_src || !d_mean || !d_std) {
        printf("ERROR: Null pointer passed to resize_bilinear_gpu\n");
        return;
    }

    if (tarW <= 0 || tarH <= 0 || srcW <= 0 || srcH <= 0) {
        printf("ERROR: Invalid dimensions in resize_bilinear_gpu: tarW=%d, tarH=%d, srcW=%d, srcH=%d\n",
               tarW, tarH, srcW, srcH);
        return;
    }

    // Calculate grid dimensions with proper bounds checking
    dim3 dimBlock(32, 32, 1);
    dim3 dimGrid((tarW + 31) / 32, (tarH + 31) / 32, 1);

    //scaled resize
    float scaled_h = (float)srcH / tarH;
    float scaled_w = (float)srcW / tarW;
    float scale = (scaled_h > scaled_w ? scaled_h : scaled_w);

    switch (tac) {
    case tactics::GPU_NEAREST:
        nearest_BGR2RGB_nhwc2nchw_norm_kernel
                <<<dimGrid, dimBlock>>>
                (d_tar, d_src, tarW, tarH, srcW, srcH, scaled_w, scaled_h, d_mean, d_std);
        break;
    case tactics::GPU_NEAREST_CENTER:
        nearest_BGR2RGB_nhwc2nchw_norm_kernel
                <<<dimGrid, dimBlock>>>
                (d_tar, d_src, tarW, tarH, srcW, srcH, scale, scale, d_mean, d_std);
        break;
    case tactics::GPU_BILINEAR:
        bilinear_BGR2RGB_nhwc2nchw_norm_kernel
                <<<dimGrid, dimBlock>>>
                (d_tar, d_src, tarW, tarH, srcW, srcH, scaled_w, scaled_h, d_mean, d_std);
        break;
    case tactics::GPU_BILINEAR_CENTER:
        bilinear_BGR2RGB_nhwc2nchw_shift_norm_kernel
                <<<dimGrid, dimBlock>>>
                (d_tar, d_src, tarW, tarH, srcW, srcH, scale, scale, d_mean, d_std);
        break;
    default:
        LOGE("ERROR: Wrong GPU resize tactics selected. Program terminated");
        return; // Changed from exit(1) to return for better error handling
    }

    // Check for kernel launch errors
    cudaError_t err = cudaGetLastError();
    if (err != cudaSuccess) {
        printf("ERROR: CUDA kernel launch failed: %s\n", cudaGetErrorString(err));
    }
}

void resize_bilinear_gpu(
    float* d_tar, uint8_t* d_src, 
    int tarW, int tarH, 
    int srcW, int srcH, 
    tactics tac) 
{
    dim3 dimBlock(32, 32, 1);
    dim3 dimGrid(tarW / 32 + 1, tarH / 32 + 1, 1);
   
    //scaled resize
    float scaled_h = (float)srcH / tarH;
    float scaled_w = (float)srcW / tarW;
    float scale = (scaled_h > scaled_w ? scaled_h : scaled_w);

    switch (tac) {
    case tactics::GPU_NEAREST:
        nearest_BGR2RGB_nhwc2nchw_kernel <<<dimGrid, dimBlock>>>
                (d_tar, d_src, tarW, tarH, srcW, srcH, scaled_w, scaled_h);
        break;
    case tactics::GPU_NEAREST_CENTER:
        nearest_BGR2RGB_nhwc2nchw_kernel <<<dimGrid, dimBlock>>>
                (d_tar, d_src, tarW, tarH, srcW, srcH, scale, scale);
        break;
    case tactics::GPU_BILINEAR:
        bilinear_BGR2RGB_nhwc2nchw_kernel <<<dimGrid, dimBlock>>> 
                (d_tar, d_src, tarW, tarH, srcW, srcH, scaled_w, scaled_h);
        break;
    case tactics::GPU_BILINEAR_CENTER:
        bilinear_BGR2RGB_nhwc2nchw_shift_kernel <<<dimGrid, dimBlock>>> 
                (d_tar, d_src, tarW, tarH, srcW, srcH, scale, scale);
        break;
    case tactics::GPU_WARP_AFFINE:
        warpaffine_init(srcH, srcW, tarH, tarW);
        warpaffine_BGR2RGB_kernel <<<dimGrid, dimBlock>>> 
                (d_tar, d_src, trans, affine_matrix);
        break;
    default:
        LOGE("ERROR: Wrong GPU resize tactics selected. Program terminated");
        exit(1);
    }
}

// -------------------------------------------------------------------------------------------------------

// Multi-batch preprocessing kernel that handles multiple images in a single kernel launch
__global__ void multi_batch_resize_normalize_kernel(
    float* d_output,           // Output tensor [batch, channels, height, width]
    uint8_t** d_input_ptrs,    // Array of pointers to input images
    int* input_widths,         // Array of input image widths
    int* input_heights,        // Array of input image heights
    int batch_size,            // Total batch size
    int active_batch_size,     // Number of actual images
    int output_height,         // Target height
    int output_width,          // Target width
    int output_channels,       // Number of channels (3)
    float* d_mean,             // Normalization mean values [3]
    float* d_std,              // Normalization std values [3]
    bool normalize             // Whether to apply normalization
) {
    // Calculate global thread indices
    int x = blockIdx.x * blockDim.x + threadIdx.x;  // Output width
    int y = blockIdx.y * blockDim.y + threadIdx.y;  // Output height
    int b = blockIdx.z * blockDim.z + threadIdx.z;  // Batch index

    // Check bounds
    if (x >= output_width || y >= output_height || b >= batch_size) {
        return;
    }

    // Calculate output memory layout offsets (NCHW format)
    int output_area = output_height * output_width;
    int batch_offset = b * output_area * output_channels;
    int pixel_idx = y * output_width + x;

    // Channel offsets in NCHW format
    int ch0_offset = batch_offset + pixel_idx;                    // R channel
    int ch1_offset = batch_offset + output_area + pixel_idx;      // G channel
    int ch2_offset = batch_offset + 2 * output_area + pixel_idx;  // B channel

    if (b < active_batch_size) {
        // Process actual image
        uint8_t* src = d_input_ptrs[b];
        int src_width = input_widths[b];
        int src_height = input_heights[b];

        // Calculate source coordinates using bilinear interpolation mapping
        float src_x = (float(x) + 0.5f) * float(src_width) / float(output_width) - 0.5f;
        float src_y = (float(y) + 0.5f) * float(src_height) / float(output_height) - 0.5f;

        // Bilinear interpolation coordinates
        int src_x1 = int(floorf(src_x));
        int src_y1 = int(floorf(src_y));
        int src_x2 = src_x1 + 1;
        int src_y2 = src_y1 + 1;

        // Check bounds and perform interpolation
        if (src_x1 >= 0 && src_y1 >= 0 && src_x2 < src_width && src_y2 < src_height) {
            // Interpolation weights
            float wx = src_x - float(src_x1);
            float wy = src_y - float(src_y1);

            float w1 = (1.0f - wx) * (1.0f - wy);  // top-left
            float w2 = wx * (1.0f - wy);           // top-right
            float w3 = (1.0f - wx) * wy;           // bottom-left
            float w4 = wx * wy;                    // bottom-right

            // Source pixel indices (NHWC format: BGR)
            int idx1 = (src_y1 * src_width + src_x1) * 3;  // top-left
            int idx2 = (src_y1 * src_width + src_x2) * 3;  // top-right
            int idx3 = (src_y2 * src_width + src_x1) * 3;  // bottom-left
            int idx4 = (src_y2 * src_width + src_x2) * 3;  // bottom-right

            // Interpolate each channel (BGR -> RGB conversion)
            float blue = w1 * src[idx1 + 0] + w2 * src[idx2 + 0] +
                        w3 * src[idx3 + 0] + w4 * src[idx4 + 0];
            float green = w1 * src[idx1 + 1] + w2 * src[idx2 + 1] +
                         w3 * src[idx3 + 1] + w4 * src[idx4 + 1];
            float red = w1 * src[idx1 + 2] + w2 * src[idx2 + 2] +
                       w3 * src[idx3 + 2] + w4 * src[idx4 + 2];

            // Normalize to [0, 1]
            red /= 255.0f;
            green /= 255.0f;
            blue /= 255.0f;

            // Apply normalization if requested
            if (normalize) {
                red = (red - d_mean[2]) / d_std[2];      // R channel (was BGR[2])
                green = (green - d_mean[1]) / d_std[1];  // G channel (was BGR[1])
                blue = (blue - d_mean[0]) / d_std[0];    // B channel (was BGR[0])
            }

            // Store in NCHW format (RGB order)
            d_output[ch0_offset] = red;    // R channel
            d_output[ch1_offset] = green;  // G channel
            d_output[ch2_offset] = blue;   // B channel
        } else {
            // Out of bounds - fill with zeros
            d_output[ch0_offset] = 0.0f;
            d_output[ch1_offset] = 0.0f;
            d_output[ch2_offset] = 0.0f;
        }
    } else {
        // Padding batch - fill with zeros
        d_output[ch0_offset] = 0.0f;
        d_output[ch1_offset] = 0.0f;
        d_output[ch2_offset] = 0.0f;
    }
}

// Optimized multi-batch preprocessing function that uses pre-allocated memory pool
void multi_batch_resize_normalize_gpu(
    float* d_output,
    uint8_t** d_input_ptrs,
    int* input_widths,
    int* input_heights,
    float* d_mean,           // Pre-allocated device memory
    float* d_std,            // Pre-allocated device memory
    const PreprocessConfig& config,
    int active_batch_size,
    cudaStream_t stream)
{
    // Input validation
    if (!d_output || !d_input_ptrs || !input_widths || !input_heights) {
        printf("ERROR: Null pointer passed to multi_batch_resize_normalize_gpu\n");
        return;
    }

    if (active_batch_size <= 0 || active_batch_size > config.batch_size) {
        printf("ERROR: Invalid active_batch_size: %d (max: %d)\n", active_batch_size, config.batch_size);
        return;
    }

    // Configure optimized kernel launch parameters
    // Use larger block sizes for better occupancy and coalesced memory access
    dim3 blockSize(32, 16, 1);  // 512 threads per block (32x16x1)
    dim3 gridSize(
        (config.input_width + blockSize.x - 1) / blockSize.x,
        (config.input_height + blockSize.y - 1) / blockSize.y,
        config.batch_size  // One block per batch
    );

    // Launch the optimized multi-batch kernel
    multi_batch_resize_normalize_kernel<<<gridSize, blockSize, 0, stream>>>(
        d_output,
        d_input_ptrs,
        input_widths,
        input_heights,
        config.batch_size,
        active_batch_size,
        config.input_height,
        config.input_width,
        config.input_channels,
        d_mean,
        d_std,
        config.normalize
    );

    // Check for kernel launch errors
    cudaError_t err = cudaGetLastError();
    if (err != cudaSuccess) {
        printf("ERROR: CUDA kernel launch failed: %s\n", cudaGetErrorString(err));
    }

    // No memory cleanup needed - using pre-allocated memory pool
}



// Global memory pool instance (thread-safe usage requires external synchronization)
static OptimizedMemoryPool g_memory_pool;

// Calculate total memory needed for all input images
size_t calculate_total_image_memory(const std::vector<cv::Mat>& input_images, int active_batch_size) {
    size_t total_size = 0;
    for (int i = 0; i < active_batch_size; i++) {
        const cv::Mat& img = input_images[i];
        total_size += img.rows * img.cols * img.channels();
    }
    return total_size;
}

// Initialize or resize memory pool if needed
bool initialize_memory_pool(const std::vector<cv::Mat>& input_images,
                           const PreprocessConfig& config,
                           int active_batch_size) {
    size_t required_buffer_size = calculate_total_image_memory(input_images, active_batch_size);

    // Add 20% padding to avoid frequent reallocations
    required_buffer_size = static_cast<size_t>(required_buffer_size * 1.2);

    bool need_realloc = !g_memory_pool.initialized ||
                       required_buffer_size > g_memory_pool.allocated_buffer_size ||
                       active_batch_size > g_memory_pool.allocated_batch_size;

    if (need_realloc) {
        g_memory_pool.cleanup();

        // Allocate single large buffer for all images
        CUDA_CHECK(cudaMalloc(&g_memory_pool.d_batch_buffer, required_buffer_size));

        // Allocate device arrays
        CUDA_CHECK(cudaMalloc(&g_memory_pool.d_input_ptrs, config.batch_size * sizeof(uint8_t*)));
        CUDA_CHECK(cudaMalloc(&g_memory_pool.d_widths, config.batch_size * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&g_memory_pool.d_heights, config.batch_size * sizeof(int)));

        // Allocate normalization parameters (reused across calls)
        if (config.normalize) {
            CUDA_CHECK(cudaMalloc(&g_memory_pool.d_mean, 3 * sizeof(float)));
            CUDA_CHECK(cudaMalloc(&g_memory_pool.d_std, 3 * sizeof(float)));
        }

        // Allocate pinned host memory for better transfer performance
        CUDA_CHECK(cudaMallocHost(&g_memory_pool.h_input_ptrs_pinned, config.batch_size * sizeof(uint8_t*)));
        CUDA_CHECK(cudaMallocHost(&g_memory_pool.h_widths_pinned, config.batch_size * sizeof(int)));
        CUDA_CHECK(cudaMallocHost(&g_memory_pool.h_heights_pinned, config.batch_size * sizeof(int)));

        g_memory_pool.allocated_buffer_size = required_buffer_size;
        g_memory_pool.allocated_batch_size = config.batch_size;
        g_memory_pool.initialized = true;
    }

    return true;
}

// Main preprocessing function that handles the complete workflow - OPTIMIZED VERSION
bool preprocess_multi_batch_gpu(
    const std::vector<cv::Mat>& input_images,
    float* d_output,
    const PreprocessConfig& config,
    int active_batch_size,
    cudaStream_t stream)
{
    if (input_images.empty() || !d_output) {
        printf("ERROR: Invalid input to preprocess_multi_batch_gpu\n");
        return false;
    }

    if (active_batch_size > static_cast<int>(input_images.size()) || active_batch_size > config.batch_size) {
        printf("ERROR: Invalid active_batch_size: %d\n", active_batch_size);
        return false;
    }

    // Initialize optimized memory pool
    if (!initialize_memory_pool(input_images, config, active_batch_size)) {
        printf("ERROR: Failed to initialize memory pool\n");
        return false;
    }

    // Prepare host data using pinned memory for better transfer performance
    uint8_t* current_buffer_pos = g_memory_pool.d_batch_buffer;

    for (int i = 0; i < active_batch_size; i++) {
        const cv::Mat& img = input_images[i];
        g_memory_pool.h_input_ptrs_pinned[i] = current_buffer_pos;
        g_memory_pool.h_widths_pinned[i] = img.cols;
        g_memory_pool.h_heights_pinned[i] = img.rows;

        // Advance buffer position for next image
        current_buffer_pos += img.rows * img.cols * img.channels();
    }

    // Single batched memory transfer for all images using pinned memory
    current_buffer_pos = g_memory_pool.d_batch_buffer;
    for (int i = 0; i < active_batch_size; i++) {
        const cv::Mat& img = input_images[i];
        size_t img_size = img.rows * img.cols * img.channels();
        CUDA_CHECK(cudaMemcpyAsync(current_buffer_pos, img.data, img_size,
                                  cudaMemcpyHostToDevice, stream));
        current_buffer_pos += img_size;
    }

    // Transfer metadata arrays using pinned memory (single transfer each)
    CUDA_CHECK(cudaMemcpyAsync(g_memory_pool.d_input_ptrs, g_memory_pool.h_input_ptrs_pinned,
                              active_batch_size * sizeof(uint8_t*),
                              cudaMemcpyHostToDevice, stream));
    CUDA_CHECK(cudaMemcpyAsync(g_memory_pool.d_widths, g_memory_pool.h_widths_pinned,
                              active_batch_size * sizeof(int),
                              cudaMemcpyHostToDevice, stream));
    CUDA_CHECK(cudaMemcpyAsync(g_memory_pool.d_heights, g_memory_pool.h_heights_pinned,
                              active_batch_size * sizeof(int),
                              cudaMemcpyHostToDevice, stream));

    // Transfer normalization parameters only once if needed
    if (config.normalize) {
        CUDA_CHECK(cudaMemcpyAsync(g_memory_pool.d_mean, config.mean, 3 * sizeof(float),
                                  cudaMemcpyHostToDevice, stream));
        CUDA_CHECK(cudaMemcpyAsync(g_memory_pool.d_std, config.std, 3 * sizeof(float),
                                  cudaMemcpyHostToDevice, stream));
    }

    // Perform optimized preprocessing using the memory pool
    multi_batch_resize_normalize_gpu(d_output, g_memory_pool.d_input_ptrs,
                                     g_memory_pool.d_widths, g_memory_pool.d_heights,
                                     g_memory_pool.d_mean, g_memory_pool.d_std,
                                     config, active_batch_size, stream);

    // No cleanup needed - memory pool is reused across calls
    return true;
}

// Function to manually clean up the global memory pool (call when shutting down)
void cleanup_preprocessing_memory_pool() {
    g_memory_pool.cleanup();
}

// Function to get memory pool statistics for debugging/monitoring
void get_memory_pool_stats(size_t* allocated_buffer_size, int* allocated_batch_size, bool* is_initialized) {
    if (allocated_buffer_size) *allocated_buffer_size = g_memory_pool.allocated_buffer_size;
    if (allocated_batch_size) *allocated_batch_size = g_memory_pool.allocated_batch_size;
    if (is_initialized) *is_initialized = g_memory_pool.initialized;
}

} // namespace preprocess
